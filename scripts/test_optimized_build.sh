#!/usr/bin/env bash

# Test script to verify that the optimized build process works correctly
# and doesn't recompile vcpkg dependencies during infinity build

set -e

echo "=== Testing Optimized Build Process ==="

# Function to check if vcpkg dependencies are being recompiled
check_vcpkg_recompilation() {
    local build_log="$1"
    
    # Check for signs of vcpkg dependency compilation
    if grep -q "Building.*vcpkg" "$build_log" || \
       grep -q "Compiling.*abseil\|arrow\|boost\|thrift\|rocksdb" "$build_log" || \
       grep -q "vcpkg install" "$build_log"; then
        echo "WARNING: vcpkg dependencies are being recompiled!"
        return 1
    else
        echo "SUCCESS: No vcpkg dependency recompilation detected"
        return 0
    fi
}

# Create a test build directory
TEST_DIR="test_optimized_build"
mkdir -p "$TEST_DIR"
cd "$TEST_DIR"

echo "Step 1: Testing container build with optimized Dockerfile..."

# Build the optimized container
echo "Building optimized container..."
docker build -f ../scripts/Dockerfile_infinity_builder_ubuntu20 -t infinity_builder:test_optimized .. 2>&1 | tee container_build.log

echo "Step 2: Testing infinity build inside container..."

# Start container
docker run -d --name infinity_test_build -v "$PWD/..:/infinity" infinity_builder:test_optimized

# Build infinity and capture output
echo "Building infinity..."
docker exec infinity_test_build bash -c "
    cd /infinity && 
    mkdir -p test_build && 
    cd test_build && 
    cmake -G Ninja -DCMAKE_BUILD_TYPE=Release -DCMAKE_VERBOSE_MAKEFILE=ON .. && 
    cmake --build . -t infinity
" 2>&1 | tee infinity_build.log

echo "Step 3: Analyzing build logs..."

# Check if vcpkg dependencies were recompiled during infinity build
if check_vcpkg_recompilation infinity_build.log; then
    echo "✅ Optimization successful: vcpkg dependencies were not recompiled during infinity build"
    RESULT="SUCCESS"
else
    echo "❌ Optimization failed: vcpkg dependencies were recompiled during infinity build"
    RESULT="FAILED"
fi

# Cleanup
echo "Step 4: Cleaning up..."
docker stop infinity_test_build || true
docker rm infinity_test_build || true
docker rmi infinity_builder:test_optimized || true

cd ..
rm -rf "$TEST_DIR"

echo "=== Test Result: $RESULT ==="

if [ "$RESULT" = "FAILED" ]; then
    exit 1
fi
