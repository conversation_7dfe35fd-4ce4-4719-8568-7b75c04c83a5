# Infinity 优化构建方案

## 问题描述

原来的构建流程中，vcpkg依赖会被多次编译：
1. `download_deps_infinity_builder_ubuntu20.sh` 使用 `vcpkg install --only-downloads` 只下载不编译
2. `Dockerfile_infinity_builder_ubuntu20` 中的 `vcpkg install` 编译一次
3. 构建infinity时，CMake又会触发vcpkg重新编译依赖

这导致构建时间很长，特别是boost、arrow、thrift等大型依赖。

## ⚠️ 重要说明

**为什么下载脚本不能编译依赖？**

下载脚本运行在宿主机环境，而容器是ubuntu20.04环境。如果在宿主机编译会导致：
- ABI不兼容（编译器版本不同）
- 系统库版本不匹配
- 架构可能不同

因此必须在容器的目标环境中编译依赖。

## 解决方案

### 1. 分离下载和编译
- **宿主机**：下载脚本只下载vcpkg源码和依赖包，不编译（避免环境不匹配）
- **容器构建时**：在正确的ubuntu20.04环境中编译vcpkg依赖
- 编译完成后将结果保存到 `/opt/vcpkg_installed`

### 2. 自动复用依赖
- 容器启动时自动检查 `/infinity/vcpkg_installed` 是否存在
- 如果不存在，自动从 `/opt/vcpkg_installed` 复制预编译的依赖
- 这样infinity构建时就能直接使用预编译的依赖，避免重复编译

### 3. 保持镜像精简
- 使用 `--clean-buildtrees-after-build --clean-packages-after-build` 清理中间文件
- 只保留编译好的库文件和头文件

## 使用方法

### 构建优化容器
```bash
cd infinity
./scripts/build_infinity_builder_ubuntu20.sh
```

### 使用容器构建infinity
```bash
# 启动容器（会自动设置vcpkg依赖）
docker run -d --name infinity_build -v $PWD:/infinity infinity_builder:ubuntu20_clang20_optimized

# 构建infinity（不会重复编译vcpkg依赖）
docker exec infinity_build bash -c "
    cd /infinity && 
    mkdir -p build && 
    cd build && 
    cmake -G Ninja -DCMAKE_BUILD_TYPE=Release .. && 
    cmake --build . -t infinity
"
```

### 测试优化效果
```bash
./scripts/test_optimized_build.sh
```

## 技术细节

### 关键修改

1. **Dockerfile_infinity_builder_ubuntu20**:
   - 预编译vcpkg依赖并保存到 `/opt/vcpkg_installed`
   - 添加启动脚本自动设置依赖

2. **download_deps_infinity_builder_ubuntu20.sh**:
   - 保持 `vcpkg install --only-downloads`（宿主机只下载，不编译）

3. **容器启动逻辑**:
   - 自动检查并复制预编译依赖到项目目录

### 预期效果

- **首次构建**: 依赖在容器构建时预编译，infinity构建时直接使用
- **后续构建**: 无需重复编译vcpkg依赖，大幅缩短构建时间
- **镜像大小**: 通过清理中间文件保持镜像精简

## 验证方法

构建日志中不应该出现：
- `Building.*vcpkg`
- `Compiling.*abseil|arrow|boost|thrift|rocksdb`
- `vcpkg install`

如果出现这些内容，说明依赖被重复编译了。
