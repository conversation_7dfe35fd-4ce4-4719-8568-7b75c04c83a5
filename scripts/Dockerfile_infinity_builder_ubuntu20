# NOTICE: This Dockerfile depends on BuildKit
# NOTICE: You must run download_deps_infinity_builder_ubuntu20.sh script BEFORE building this image
# NOTICE: The script downloads all dependencies including vcpkg packages to avoid network issues during build
# NOTICE: Use build_infinity_builder_ubuntu20.sh for automated build process

FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive
SHELL ["/bin/bash", "-c"]

# Configure APT to use USTC mirrors and install basic packages
RUN --mount=type=cache,id=inf_apt,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,id=inf_lib,target=/var/lib/apt,sharing=locked \
    apt-get update && apt-get install -y ca-certificates \
    && sed -i 's|http://archive.ubuntu.com|https://mirrors.ustc.edu.cn|g' /etc/apt/sources.list \
    && sed -i 's|http://security.ubuntu.com|https://mirrors.ustc.edu.cn|g' /etc/apt/sources.list \
    && apt-get update && apt-get install -y \
        git make wget curl vim file gettext \
        libssl-dev unzip bzip2 libtool m4 tree \
        autoconf automake pkg-config \
        software-properties-common gnupg2 lsb-release \
        build-essential zlib1g-dev libncurses5-dev libgdbm-dev \
        libnss3-dev libreadline-dev libffi-dev libsqlite3-dev \
        libbz2-dev libexpat1-dev liblzma-dev tk-dev uuid-dev \
        postgresql graphviz sudo bridge-utils iptables \
        libpcap-dev libpcre3-dev \
        perl flex bison binutils gdb \
    && echo '/usr/local/lib' >> /etc/ld.so.conf.d/local.conf \
    && echo '/usr/local/lib64' >> /etc/ld.so.conf.d/local.conf

# Add LLVM APT repository and install clang 20
RUN --mount=type=cache,id=inf_apt,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,id=inf_lib,target=/var/lib/apt,sharing=locked \
    wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key | apt-key add - \
    && echo "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-20 main" >> /etc/apt/sources.list.d/llvm.list \
    && echo "deb-src http://apt.llvm.org/focal/ llvm-toolchain-focal-20 main" >> /etc/apt/sources.list.d/llvm.list \
    && apt-get update \
    && apt-get install -y \
        clang-20 clang++-20 clang-tools-20 \
        libc++-20-dev libc++abi-20-dev \
        lld-20 lldb-20 \
    && update-alternatives --install /usr/bin/clang clang /usr/bin/clang-20 100 \
    && update-alternatives --install /usr/bin/clang++ clang++ /usr/bin/clang++-20 100 \
    && update-alternatives --install /usr/bin/lld lld /usr/bin/lld-20 100

# Install GCC 15 build dependencies
RUN --mount=type=cache,id=inf_apt,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,id=inf_lib,target=/var/lib/apt,sharing=locked \
    apt install -y libgmp-dev libmpfr-dev libmpc-dev libisl-dev

# Install GCC 15 from source with complete libstdc++ support
RUN --mount=type=bind,source=gcc-15.1.0.tar.gz,target=/root/gcc-15.1.0.tar.gz \
    cd /root && tar xf gcc-15.1.0.tar.gz \
    && cd gcc-releases-gcc-15.1.0 && mkdir build && cd build \
    && ../configure --prefix=/usr \
        --enable-languages=c,c++ \
        --enable-shared \
        --enable-static \
        --enable-libstdcxx \
        --enable-libstdcxx-time \
        --enable-libstdcxx-filesystem-ts \
        --disable-multilib \
        --with-system-zlib \
        --program-suffix=-15 \
    && make -j$(nproc) \
    && make install \
    && update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-15 150 \
    && update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-15 150 \
    && update-alternatives --install /usr/bin/cpp cpp /usr/bin/cpp-15 150 \
    && cd /root && rm -rf gcc-releases-gcc-15.1.0

# Configure GCC 15 library paths and symlinks for infinity build system
RUN mkdir -p /usr/lib64/gcc/15 \
    && mkdir -p /usr/lib64/gcc/x86_64-linux-gnu/15 \
    # Copy static libraries to expected locations
    && cp /usr/lib64/libstdc++.a /usr/lib64/gcc/15/ \
    && cp /usr/lib64/libstdc++exp.a /usr/lib64/gcc/15/ \
    && cp /usr/lib64/libstdc++.a /usr/lib64/gcc/x86_64-linux-gnu/15/ \
    && cp /usr/lib64/libstdc++exp.a /usr/lib64/gcc/x86_64-linux-gnu/15/ \
    # Update library paths and ensure new libstdc++ is used
    && echo "/usr/lib64" >> /etc/ld.so.conf.d/gcc-15.conf \
    && echo "/usr/lib/x86_64-linux-gnu" >> /etc/ld.so.conf.d/gcc-15.conf \
    && ldconfig \
    # Create symlinks for dynamic libraries
    && ln -sf /usr/lib64/libstdc++.so.6.0.34 /lib/x86_64-linux-gnu/libstdc++.so.6 \
    && ln -sf /usr/lib64/libstdc++.so.6.0.34 /usr/lib/x86_64-linux-gnu/libstdc++.so.6 \
    # Verify the symlink points to the correct version
    && ls -la /lib/x86_64-linux-gnu/libstdc++.so.6

# Install Python 3.10 from source
RUN --mount=type=bind,source=Python-3.10.17.tar.xz,target=/root/Python-3.10.17.tar.xz \
    cd /root && tar xJf Python-3.10.17.tar.xz \
    && cd Python-3.10.17 && ./configure --enable-shared \
    && make -j$(nproc) && make altinstall \
    && ldconfig && cd /root && rm -rf Python-3.10.17 \
    && update-alternatives --install /usr/bin/python3 python3 /usr/local/bin/python3.10 50 \
    && update-alternatives --install /usr/bin/pip3 pip3 /usr/local/bin/pip3.10 50

# Install cmake-3.31.7
RUN --mount=type=bind,source=cmake-3.31.7-linux-x86_64.tar.gz,target=/root/cmake-3.31.7-linux-x86_64.tar.gz \
    cd /root && tar xf cmake-3.31.7-linux-x86_64.tar.gz \
    && cp -rf cmake-3.31.7-linux-x86_64/bin/* /usr/local/bin \
    && cp -rf cmake-3.31.7-linux-x86_64/share/* /usr/local/share \
    && rm -rf cmake-3.31.7-linux-x86_64

# Install ninja-1.12.1
RUN --mount=type=bind,source=ninja-linux.zip,target=/root/ninja-linux.zip \
    cd /root && unzip ninja-linux.zip \
    && cp ninja /usr/local/bin && rm ninja


# Install boost-1.86.0
RUN --mount=type=bind,source=boost-1.86.0-cmake.tar.xz,target=/root/boost-1.86.0-cmake.tar.xz \
    cd /root && tar xf boost-1.86.0-cmake.tar.xz \
    && cd boost-1.86.0 && mkdir build && cd build \
    && cmake .. -DCMAKE_INSTALL_PREFIX=/usr/local -DBUILD_SHARED_LIBS=OFF -DBOOST_ENABLE_PYTHON=OFF \
    && make -j && make install \
    && ldconfig && cd /root && rm -rf boost-1.86.0

# Install libevent-2.1.12
RUN --mount=type=bind,source=libevent-2.1.12-stable.tar.gz,target=/root/libevent-2.1.12-stable.tar.gz  \
    cd /root && tar xf libevent-2.1.12-stable.tar.gz \
    && cd libevent-2.1.12-stable && mkdir build && cd build \
    && cmake -G Ninja -DCMAKE_BUILD_TYPE=Release \
        -DCMAKE_C_FLAGS="-fPIC" .. \
    && ninja install \
    && ldconfig && cd /root && rm -rf libevent-2.1.12-stable

# Install lz4-1.10.0
RUN --mount=type=bind,source=lz4-1.10.0.tar.gz,target=/root/lz4-1.10.0.tar.gz  \
    cd /root && tar xf lz4-1.10.0.tar.gz \
    && cd lz4-1.10.0 && CFLAGS="-fPIC" make -j install \
    && ldconfig && cd /root && rm -rf lz4-1.10.0

# Install zlib-1.3.1
RUN --mount=type=bind,source=zlib-1.3.1.tar.gz,target=/root/zlib-1.3.1.tar.gz  \
    cd /root && tar xf zlib-1.3.1.tar.gz \
    && cd zlib-1.3.1 && ./configure && CFLAGS="-fPIC" make -j install \
    && ldconfig && cd /root && rm -rf zlib-1.3.1

# Install zstd-1.5.7
RUN --mount=type=bind,source=zstd-1.5.7.tar.gz,target=/root/zstd-1.5.7.tar.gz  \
    cd /root && tar xf zstd-1.5.7.tar.gz \
    && cd zstd-1.5.7 && CFLAGS="-fPIC" CXXFLAGS="-fPIC" make -j lib-mt && make install \
    && ldconfig && cd /root && rm -rf zstd-1.5.7

# Install bzip2-1.0.8
RUN --mount=type=bind,source=bzip2-1.0.8.tar.gz,target=/root/bzip2-1.0.8.tar.gz  \
    cd /root && tar xf bzip2-1.0.8.tar.gz \
    && cd bzip2-1.0.8 && make install \
    && ldconfig && cd /root && rm -rf bzip2-1.0.8

# Install brotli-1.1.0
RUN --mount=type=bind,source=v1.1.0.tar.gz,target=/root/v1.1.0.tar.gz  \
    cd /root && tar xf v1.1.0.tar.gz \
    && cd brotli-1.1.0 && mkdir out && cd out && cmake -DCMAKE_BUILD_TYPE=Release .. && cmake --build . --config Release --target install \
    && ldconfig && cd /root && rm -rf brotli-1.1.0

# Install jemalloc-5.3.0
# Known issue: Composition of `-fsanitize=address`, staticly linked jemalloc and `mallctl` cause crash at initialization.
# Refers to https://github.com/jemalloc/jemalloc/issues/2454
RUN --mount=type=bind,source=jemalloc-5.3.0.tar.bz2,target=/root/jemalloc-5.3.0.tar.bz2  \
    cd /root && tar xjf jemalloc-5.3.0.tar.bz2 \
    && cd jemalloc-5.3.0 && CFLAGS="-fPIC" CXXFLAGS="-fPIC" ./configure --enable-static --disable-libdl --enable-prof --enable-prof-libunwind --disable-initial-exec-tls && make -j install \
    && ldconfig && cd /root && rm -rf jemalloc-5.3.0

# Install gperftools-2.15
RUN --mount=type=bind,source=gperftools-2.15.tar.gz,target=/root/gperftools-2.15.tar.gz  \
    cd /root && tar xzf gperftools-2.15.tar.gz \
    && cd gperftools-2.15 && ./configure && make -j && make install \
    && ldconfig && cd /root && rm -rf gperftools-2.15

# Install sqllogictest
RUN --mount=type=bind,source=sqllogictest-bin-v0.28.2-x86_64-unknown-linux-musl.tar.gz,target=/root/sqllogictest-bin-v0.28.2-x86_64-unknown-linux-musl.tar.gz \
    cd /tmp && tar xzf /root/sqllogictest-bin-v0.28.2-x86_64-unknown-linux-musl.tar.gz && cp -rf sqllogictest /usr/local/bin && rm -fr /tmp/*

# Install colm
RUN --mount=type=bind,source=colm-bd19b1ab7c0bcdba2772061bc38f6bca5cbba977.zip,target=/root/colm-bd19b1ab7c0bcdba2772061bc38f6bca5cbba977.zip  \
    cd /root && unzip colm-bd19b1ab7c0bcdba2772061bc38f6bca5cbba977.zip \
    && cd colm-bd19b1ab7c0bcdba2772061bc38f6bca5cbba977 \
    && ./autogen.sh \
    && ./configure --prefix=/usr/local/colm \
    && make -j 8 \
    && make install \
    && rm -rf ../colm-bd19b1ab7c0bcdba2772061bc38f6bca5cbba977

# Install ragel
RUN --mount=type=bind,source=ragel-master.zip,target=/root/ragel-master.zip  \
    cd /root && unzip ragel-master.zip \
    && cd ragel-master \
    && ./autogen.sh \
    && ./configure --prefix=/usr/local/ragel --with-colm=/usr/local/colm \
    && make -j 12 install \
    && rm -rf ../ragel-master

RUN --mount=type=cache,id=inf_apt,target=/var/cache/apt,sharing=locked \
    --mount=type=cache,id=inf_lib,target=/var/lib/apt,sharing=locked \
    apt-get install -y zip rpm

# Copy pre-downloaded vcpkg and dependencies
COPY vcpkg /root/vcpkg

ENV VCPKG_ROOT=/root/vcpkg \
    PATH=/root/vcpkg:${PATH}

# Install vcpkg dependencies from pre-downloaded cache
# Clean build trees and packages to keep image size small, but keep installed packages
RUN --mount=type=bind,source=vcpkg.json,target=/root/vcpkg.json \
    cd /root && vcpkg install --clean-buildtrees-after-build --clean-packages-after-build

# Create a template vcpkg_installed directory that can be copied to project builds
# This avoids recompilation when building infinity
RUN mkdir -p /opt/vcpkg_installed && \
    cp -r /root/vcpkg/installed/* /opt/vcpkg_installed/ && \
    cp -r /root/vcpkg/vcpkg /opt/vcpkg_installed/

# Set environment variables for infinity build
ENV CC=clang \
    CXX=clang++ \
    LZ4_ROOT=/usr/local \
    PATH=/usr/local/bin:/usr/local/ragel/bin:$PATH \
    CMAKE_TOOLCHAIN_FILE=/root/vcpkg/scripts/buildsystems/vcpkg.cmake

# Set Python paths for CMake to find the correct Python installation
ENV Python3_EXECUTABLE=/usr/local/bin/python3.10 \
    Python3_INCLUDE_DIR=/usr/local/include/python3.10 \
    Python3_LIBRARY=/usr/local/lib/libpython3.10.so

# Create a script to setup vcpkg dependencies for infinity builds
RUN echo '#!/bin/bash\n\
if [ -d "/infinity" ] && [ ! -d "/infinity/vcpkg_installed" ]; then\n\
    echo "Setting up pre-compiled vcpkg dependencies for infinity..."\n\
    cp -r /opt/vcpkg_installed /infinity/\n\
    echo "vcpkg dependencies ready for use"\n\
fi\n\
exec "$@"' > /usr/local/bin/setup-vcpkg.sh && \
    chmod +x /usr/local/bin/setup-vcpkg.sh

# Install vectorscan
RUN --mount=type=bind,source=vectorscan-master.zip,target=/root/vectorscan-master.zip \
    cd /root && unzip vectorscan-master.zip \
    && cd vectorscan-master\
    && mkdir build \
    && cd build \
    && cmake -DCMAKE_BUILD_TYPE=Release -DFAT_RUNTIME=Off ..  \
    && make -j 12 install \
    && rm -rf ../../vectorscan-master

# Install mold-2.40.3
RUN --mount=type=bind,source=mold-2.40.3-x86_64-linux.tar.gz,target=/root/mold-2.40.3-x86_64-linux.tar.gz \
    cd /root && tar xf mold-2.40.3-x86_64-linux.tar.gz \
    && cp -rf mold-2.40.3-x86_64-linux/bin/* /usr/local/bin \
    && cp -rf mold-2.40.3-x86_64-linux/lib/* /usr/local/lib \
    && cp -rf mold-2.40.3-x86_64-linux/libexec/* /usr/local/libexec \
    && rm -rf mold-2.40.3-x86_64-linux

RUN pip3 config set global.index-url https://mirrors.aliyun.com/pypi/simple && pip3 config set global.trusted-host mirrors.aliyun.com

# Create a python virtual environment for 3.11
RUN --mount=type=bind,source=python/requirements.txt,target=/root/requirements_1.txt --mount=type=bind,source=python/benchmark/requirements.txt,target=/root/requirements_2.txt  --mount=type=bind,source=python/restart_test/requirements.txt,target=/root/requirements_3.txt --mount=type=bind,source=python/test_cluster/requirements.txt,target=/root/requirements_4.txt \
    pip3 install --no-input wheel auditwheel patchelf twine \
    && pip3 install --no-input -r /root/requirements_1.txt -r /root/requirements_2.txt -r /root/requirements_3.txt -r /root/requirements_4.txt

# Install iproute2
RUN --mount=type=bind,source=iproute2-6.9.0.tar.gz,target=/root/iproute2-6.9.0.tar.gz \
cd /root \
&& tar -zxf iproute2-6.9.0.tar.gz && cd iproute2-6.9.0 \
&& ./configure --prefix=/usr/local/iproute2 \
&& make && make install \
&& ip -V

# Install Docker
RUN --mount=type=bind,source=docker-27.4.1.tgz,target=/root/docker-27.4.1.tgz \
    cd /root \
    && tar zxf docker-27.4.1.tgz \
    && cp docker/* /usr/bin

# No special configuration needed as GCC-15 is installed in /usr

ENTRYPOINT ["/usr/local/bin/setup-vcpkg.sh", "bash", "-c", "while true; do sleep 60; done"]
