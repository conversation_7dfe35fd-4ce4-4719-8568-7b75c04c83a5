#!/usr/bin/env bash

# Build script for infinity builder ubuntu20 container
# This script demonstrates the optimized build process that avoids recompiling vcpkg dependencies

set -e

echo "=== Building Infinity Builder Ubuntu20 Container ==="

# Check if we're in the right directory
if [ ! -f "scripts/Dockerfile_infinity_builder_ubuntu20" ]; then
    echo "Error: Please run this script from the infinity project root directory"
    exit 1
fi

# Download dependencies first
echo "Step 1: Downloading and pre-compiling dependencies..."
cd scripts
./download_deps_infinity_builder_ubuntu20.sh
cd ..

# Build the container
echo "Step 2: Building Docker container..."
docker build -f scripts/Dockerfile_infinity_builder_ubuntu20 -t infinity_builder:ubuntu20_clang20_optimized .

echo "=== Container built successfully ==="
echo "You can now use this container to build infinity without recompiling vcpkg dependencies:"
echo "docker run -d --name infinity_build -v \$PWD:/infinity infinity_builder:ubuntu20_clang20_optimized"
echo "docker exec infinity_build bash -c 'cd /infinity && mkdir -p build && cd build && cmake -G Ninja -DCMAKE_BUILD_TYPE=Release .. && cmake --build . -t infinity'"
