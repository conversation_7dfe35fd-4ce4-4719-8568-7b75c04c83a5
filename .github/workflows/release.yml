name: release

on:
  schedule:
    - cron: '0 13 * * *'  # This schedule runs every 13:00:00Z(21:00:00+08:00)
  # The "create tags" trigger is specifically focused on the creation of new tags, while the "push tags" trigger is activated when tags are pushed, including both new tag creations and updates to existing tags.
  create:
    tags:
      - "v*.*.*"                  # normal release
      - "nightly"                 # the only one mutable tag

# https://docs.github.com/en/actions/using-jobs/using-concurrency
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  release:
    runs-on: [ "ubuntu-latest" ]
    steps:
    - name: Ensure workspace ownership
      run: echo "chown -R $USER $GITHUB_WORKSPACE" && sudo chown -R $USER $GITHUB_WORKSPACE

    # https://github.com/actions/checkout/blob/v3/README.md
    - name: Check out code
      uses: actions/checkout@v4
      with:
        ssh-key: ${{ secrets.MY_DEPLOY_KEY }}

    - name: Prepare release body
      run: |
        if [[ $GITHUB_EVENT_NAME == 'create' ]]; then
          RELEASE_TAG=${GITHUB_REF#refs/tags/}
          if [[ $RELEASE_TAG == 'nightly' ]]; then
            PRERELEASE=true
          else
            PRERELEASE=false
          fi
          echo "Workflow triggered by create tag: $RELEASE_TAG"
        else
          RELEASE_TAG=nightly
          PRERELEASE=true
          echo "Workflow triggered by schedule"
        fi
        echo "RELEASE_TAG=$RELEASE_TAG" >> $GITHUB_ENV
        echo "PRERELEASE=$PRERELEASE" >> $GITHUB_ENV
        RELEASE_DATETIME=$(date --rfc-3339=seconds)
        cat <<EOF > release_template.md
        Release $RELEASE_TAG created from $GITHUB_SHA at $RELEASE_DATETIME
        EOF
        envsubst < release_template.md > release_body.md

    - name: Move the existing mutable tag
      # https://github.com/softprops/action-gh-release/issues/171
      run: |
        if [[ $GITHUB_EVENT_NAME == 'schedule' ]]; then
          # Determine if a given tag exists and matches a specific Git commit.
          # actions/checkout@v4 fetch-tags doesn't work when triggered by schedule
          git fetch --tags
          if [ "$(git rev-parse -q --verify "refs/tags/$RELEASE_TAG")" = "$GITHUB_SHA" ]; then
            echo "mutable tag $RELEASE_TAG exists and matches $GITHUB_SHA"
          else
            git tag -f $RELEASE_TAG $GITHUB_SHA
            git push -f origin $RELEASE_TAG:refs/tags/$RELEASE_TAG
            echo "created/moved mutable tag $RELEASE_TAG to $GITHUB_SHA"
          fi
        fi

    - name: Start builder container
      run: |
        BUILDER_CONTAINER=infinity_build_$(od -An -N4 -t u8 < /dev/urandom | tr -d ' ')
        echo "BUILDER_CONTAINER=${BUILDER_CONTAINER}" >> $GITHUB_ENV
        TZ=$(readlink -f /etc/localtime | awk -F '/zoneinfo/' '{print $2}')
        sudo docker rm -f ${BUILDER_CONTAINER} && sudo docker run -d --name ${BUILDER_CONTAINER} -e TZ=$TZ -v $PWD:/infinity infiniflow/infinity_builder:ubuntu20_clang20

    - name: Build release version
      run: |
        sudo docker exec ${BUILDER_CONTAINER} bash -c "git config --global safe.directory \"*\" && cd /infinity && rm -fr cmake-build-reldeb && mkdir -p cmake-build-reldeb && cmake -G Ninja -DCMAKE_BUILD_TYPE=RelWithDebInfo -DCPACK_PACKAGE_VERSION=${{ env.RELEASE_TAG }} -DCPACK_DEBIAN_PACKAGE_ARCHITECTURE=amd64 -DCMAKE_JOB_POOLS:STRING='link=1' -DCMAKE_TOOLCHAIN_FILE=${VCPKG_ROOT}/scripts/buildsystems/vcpkg.cmake -DCI=ON -S /infinity -B /infinity/cmake-build-reldeb && cmake --build /infinity/cmake-build-reldeb --target infinity"

    - name: Build RPM and DEB
      run: sudo docker exec ${BUILDER_CONTAINER} bash -c "cd /infinity/cmake-build-reldeb && cpack"

    - name: Create or overwrite a release
      # https://github.com/actions/upload-release-asset has been replaced by https://github.com/softprops/action-gh-release
      uses: softprops/action-gh-release@v2
      with:
        token: ${{ secrets.MY_GITHUB_TOKEN }}  # Use the secret as an environment variable
        prerelease: ${{ env.PRERELEASE }}
        tag_name: ${{ env.RELEASE_TAG }}
        # The body field does not support environment variable substitution directly.
        body_path: release_body.md
        files: |
          cmake-build-reldeb/infinity-*.deb
          cmake-build-reldeb/infinity-*.rpm
          cmake-build-reldeb/infinity-*.tar.gz

    - name: Set up QEMU
      uses: docker/setup-qemu-action@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    # https://github.com/marketplace/actions/docker-login
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: infiniflow
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    # https://github.com/marketplace/actions/build-and-push-docker-images
    - name: Build and push
      uses: docker/build-push-action@v6
      with:
        context: .
        push: true
        tags: infiniflow/infinity:${{ env.RELEASE_TAG }}
        file: scripts/Dockerfile_infinity

    - name: Build infinity-sdk
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        sudo rm -fr cmake-build-reldeb
        sudo docker exec ${BUILDER_CONTAINER} bash -c "cd /infinity/ && rm -rf wheelhouse && pip3 wheel ./python/infinity_sdk -v -w wheelhouse --no-deps"

    - name: Publish package distributions to PyPI
      if: startsWith(github.ref, 'refs/tags/v')
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        packages-dir: wheelhouse/
        password: ${{ secrets.PYPI_API_TOKEN }}
        verbose: true

    - name: Destroy builder container
      if: always()  # always run this step even if previous steps failed
      run: |
        if [ -n "${BUILDER_CONTAINER}" ]; then
          sudo docker rm -f -v ${BUILDER_CONTAINER}
        fi